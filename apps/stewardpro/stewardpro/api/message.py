import frappe
import requests
from requests.auth import HTTPBasicAuth

@frappe.whitelist()
def send_sms(doc, method=None):
    if isinstance(doc, str):
        doc = frappe._dict(frappe.parse_json(doc))
    
    message = doc.message
    receivers = []
    for receiver in doc.recipients:
        receivers.append({"recipient_id": receiver['member_name'], "dest_addr": receiver['phone_number']})
    
    url = "https://apisms.beem.africa/v1/send"

    data = {
        "source_addr": "INFO",
        "encoding": 0,
        "message": message,
        "recipients": receivers
    }

    username = "2aedf635387b5e73"
    password = "NjQyOTMwODU3ZTE2NGM1OTJiY2E3MDEwOGYxZmE1MTU3YzYzNzlkNGYxYTIyMGQ0ZmRmNzk4ZWZlZWY5YjgyOA=="

    response = requests.post(url, json=data, auth=HTTPBasicAuth(username, password))

    if response.status_code == 200:
        print("SMS sent successfully!")
    else:
        print("SMS sending failed. Status code:", response.status_code)
        print("Response:", response.text)